import { StyleProp, StyleSheet, View, ViewStyle,  } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

export const CardPlayGradientBg = ({
  children,
  contentContainerStyle,
}:{
  children: React.ReactNode;
  contentContainerStyle?: StyleProp<ViewStyle>;
}) => {
  return (
    <LinearGradient
      colors={[
        'rgba(41, 5, 72, 1)',
        'rgba(64, 0, 64, 1)',
        'rgba(153, 48, 116, 1)',
        'rgba(255, 172, 78, 1)',
      ]}
      locations={[0, 0.53, 0.83, 0.99]}
      style={styles.container}
    >
      <View style={[styles.contentContainer, contentContainerStyle]}>
        {children}
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 16,
    width: '100%',
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
  },
});
