import CardButton from '@/partials/card-button';
import CardShadowButton from '@/partials/card-shadow-button';
import { FONTS } from '@constants/fonts';
import { BlurView } from '@react-native-community/blur';
import XIcon from '@src/icons/XIcon';
import React from 'react';
import {
  View,
  StyleSheet,
  Text,
  Modal,
  Dimensions,
  Pressable,
} from 'react-native';

const { width, height } = Dimensions.get('window');

const ConfirmExitModal = ({
  visible = false,
  onClose = () => {},
  onYes = () => {},
}: {
  visible?: boolean;
  backgroundColor?: string;
  text?: string;
  onClose?: () => void;
  onYes?: () => void;
}) => {

  return (
    <Modal
      id="settings-modal"
      transparent={true}
      animationType="fade"
      visible={visible}
      onRequestClose={() => {}}
      style={styles.overlay}
    >
      <BlurView
        blurType="dark"
        blurAmount={20}
        reducedTransparencyFallbackColor="white"
        style={[styles.blurView]}
      >
        <View style={[styles.contentContainer]}>
          <View style={styles.container}>
            <View style={styles.closeButtonContainer}>
              <Pressable onPress={onClose} style={styles.closeButtonContainer}>
                <XIcon width={24} height={24} />
              </Pressable>
            </View>
            <Text style={styles.title}>
              Exit
            </Text>
            <Text style={styles.label}>
              Are you sure you want to leave this game?
            </Text>
            <View style={styles.btnContainer}>
              <CardButton
                title="Exit Anyway"
                containerStyle={{ width: 'auto' }}
                onPress={onYes}
              />
              <CardShadowButton
                onPress={onClose}
                containerStyle={{ width: 100 }}
              >
                <Text style={styles.label}>Stay</Text>
              </CardShadowButton>
            </View>
          </View>
        </View>
      </BlurView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    justifyContent: 'center',
    alignItems: 'center',
    width: width,
    height: height,
    position: 'relative',
  },
  closeButtonContainer: {
    position: 'absolute',
    top: 6,
    right: 10,
    padding: 8,
    zIndex: 1,
  },
  container: {
    backgroundColor: '#0B1713B2',
    paddingHorizontal: 24,
    paddingVertical: 32,
    borderRadius: 40,
    position: 'relative',
    gap: 24,
  },
  spinner: {
    marginBottom: 15,
  },
  text: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  blurView: {
    flex: 1,
  },
  backgroundStyle: {
    backgroundColor: 'transparent',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: 600,
    fontFamily: FONTS.CRIMSON_TEXT.REGULAR,
    color: 'rgba(255, 255, 255)',
  },
  labelContainer: {
    gap: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: 400,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: 'rgba(255, 255, 255, 0.85)',
    lineHeight: 24,
  },
  labelItemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  languageContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  labelItem: {
    gap: 8,
  },
  labelDesc: {
    fontSize: 13,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: '#8D8D8D',
    lineHeight: 16,
  },
  btnContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});

export default ConfirmExitModal;
