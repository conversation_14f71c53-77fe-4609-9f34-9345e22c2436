import { FONTS } from '@constants/fonts';
import { BlurView } from '@react-native-community/blur';
import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Text, Modal, Dimensions } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Rive from 'rive-react-native';

const { width, height } = Dimensions.get('window');

const PreparePlayLayer = ({
  visible = false,
}: {
  visible?: boolean;
  backgroundColor?: string;
  text?: string;
}) => {
  const insets = useSafeAreaInsets();

  const TEXT = [
    {
      title: `Let's set the perfect mood...`,
      icon: null,
      desc: null,
    },
    {
      title: `We Listen, But We Don't Judge`,
      icon: '🤲',
      desc: null,
    },
    {
      title: `Soft music background`,
      icon: '🎹',
      desc: null,
    },
    {
      title: `Light a candle and wine.`,
      icon: '🍷',
      desc: 'No driving after drinking. Stay in, stay close',
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex(prevIndex => (prevIndex + 1) % TEXT.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [TEXT.length]);

  return (
    <Modal
      transparent={true}
      animationType="fade"
      visible={visible}
      onRequestClose={() => {}}
      style={styles.overlay}
    >
      <View style={[styles.fireContainer, { top: insets.top }]}>
        <Rive
          url='https://cdn.jsdelivr.net/gh/mrlexz/love-deck-cdn@main/assets/heart_brigde_v2.riv'
          style={styles.riveAnimation}
          autoplay
        />
      </View>
      <BlurView
        blurType="dark"
        blurAmount={15}
        reducedTransparencyFallbackColor="white"
        style={[styles.blurView]}
      >
        <View
          style={[
            styles.contentContainer,
            { paddingTop: insets.top, paddingBottom: insets.bottom },
          ]}
        >
          <View style={styles.hideFireContainer}></View>
          <View style={styles.titleContainer}>
            {TEXT[currentIndex]?.icon && (
              <Text style={styles.title}>{TEXT[currentIndex].icon}</Text>
            )}
            <Text style={styles.title}>{TEXT[currentIndex].title}</Text>
          </View>
          {TEXT[currentIndex]?.desc ? (
            <View style={styles.descriptionContainer}>
              <Text style={styles.description}>🚫🍺🍺🚫</Text>
              <Text style={styles.description}>{TEXT[currentIndex].desc}</Text>
            </View>
          ) : (
            <View style={styles.hideDescriptionContainer}></View>
          )}
        </View>
      </BlurView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: width,
    height: height,
    position: 'relative',
  },
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 20,
    borderRadius: 10,
    minWidth: 120,
    minHeight: 120,
  },
  spinner: {
    marginBottom: 15,
  },
  text: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  blurView: {
    flex: 1,
  },
  backgroundStyle: {
    backgroundColor: 'transparent',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 40,
  },
  hideFireContainer: {
    height: 60,
  },
  fireContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  title: {
    fontSize: 32,
    fontFamily: FONTS.BALOO2.EXTRA_BOLD,
    color: 'rgba(255, 255, 255, 0.85)',
    textAlign: 'center',
  },
  descriptionContainer: {
    paddingHorizontal: 70,
  },
  hideDescriptionContainer: {
    height: 50,
  },
  riveAnimation: {
    width: 60,
    height: 60,
  },
  titleContainer: {},
  description: {
    fontSize: 14,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: 'rgba(255, 255, 255, 0.85)',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default PreparePlayLayer;
