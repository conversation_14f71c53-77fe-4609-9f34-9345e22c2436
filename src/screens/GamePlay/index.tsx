import React, { useMemo, useRef, useState } from 'react';
import { FONTS } from '@constants/fonts';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CardButton from '@/partials/card-button';
import CardShadowButton from '@/partials/card-shadow-button';
import { StatusBar } from 'react-native';
import ControlButtons from '@src/components/ControlButtons';
import SettingsModal from '@src/components/SettingsModal';
import LanguageBS from '@src/components/LanguageBS';
import AuroraBackground from '@src/components/AuroraBackground';
import ArrowRightBig from '@src/icons/ArrowRightBigIcon';
import { CardPlayGradientBg } from '@src/components/CardPlayGradientBg';
import { useNavigation } from '@react-navigation/native';
import { HomeScreenNavigationProp } from '@/types/navigation';
import { useAppContext } from '@src/context/AppContext';
import ConfirmExitModal from '@src/components/ConfirmExitModal';
import { useTypedTranslation } from '@src/hooks/useTypedTranslation';

import {
  PanGestureHandler,
  PanGesture,
  PanGestureHandlerGestureEvent,
  State,
  GestureDetector,
  Gesture,
} from 'react-native-gesture-handler';
import { Option } from '@/types/database';
import GradientBorderButton from '@/partials/button-w-gradient';

const { width: screenWidth } = Dimensions.get('window');
const SWIPE_THRESHOLD = screenWidth * 0.1; // 30% của màn hình

export default function GamePlay() {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { state } = useAppContext();

  // Refs cho animation
  const panRefs = useRef<{ [key: string]: any }>({});
  const translateXValues = useRef<{ [key: string]: Animated.Value }>({});

  const [currentQuestion, setCurrentQuestion] = useState(42);

  const [selectedAnswers, setSelectedAnswers] = useState<Option[]>([]);
  console.log('🚀 ~ GamePlay ~ selectedAnswers:', selectedAnswers);

  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isConfirmExitModalOpen, setIsConfirmExitModalOpen] =
    React.useState(false);

  const { t } = useTypedTranslation();

  const question = useMemo(() => {
    if (!state.questions || state.questions.length === 0) return null;
    const safeIndex = currentQuestion % state.questions.length;
    return state.questions[safeIndex];
  }, [currentQuestion, state.questions]);

  const options = useMemo(() => {
    if (!question) return [];

    if (!question.question_variants) return [];

    if (question.question_variants.length === 0) return [];

    if (question.question_variants[0].name === 'multiple_choice') {
      return question.question_variants[0].options || [];
    }

    return [];
  }, [question]);

  const example = useMemo(() => {
    if (!question) return null;
    return state.language === 'en' ? question.example_en : question.example_vi;
  }, [question, state.language]);

  const onNextQuestion = () => {
    setCurrentQuestion(prev => {
      if (prev >= state.questions.length - 1) {
        return 0;
      }
      return prev + 1;
    });
  };

  const onShuffleQuestion = () => {
    setCurrentQuestion(Math.floor(Math.random() * state.questions.length));
  };

  const onHandleGoBack = () => {
    setIsConfirmExitModalOpen(true);
  };

  const onHandleYes = () => {
    setIsConfirmExitModalOpen(false);
    navigation.goBack();
  };

  const handleSelectAnswer = (option: Option) => {
    // Kiểm tra xem đã chọn chưa
    if (selectedAnswers.find(answer => answer.id === option.id)) {
      return;
    }

    // Thêm câu trả lời mới
    const newAnswer = {
      ...option,
      timestamp: Date.now(),
      zIndex: selectedAnswers.length + 1,
    };

    setSelectedAnswers(prev => [...prev, newAnswer]);

    // Khởi tạo animated value cho option mới
    translateXValues.current[option.id] = new Animated.Value(0);
  };

  const handleGestureEvent = (optionId: string) => {
    return Animated.event(
      [{ nativeEvent: { translationX: translateXValues.current[optionId] } }],
      { useNativeDriver: true },
    );
  };

  const handleStateChange = (optionId: string) => {
    return ({ nativeEvent }: PanGestureHandlerGestureEvent) => {
      if (nativeEvent.state === State.END) {
        const { translationX } = nativeEvent;

        // Nếu vuốt đủ xa thì xóa
        if (Math.abs(translationX) > SWIPE_THRESHOLD) {
          removeAnswer(optionId, translationX);
        } else {
          // Nếu không đủ xa thì trả về vị trí ban đầu
          Animated.spring(translateXValues.current[optionId], {
            toValue: 0,
            useNativeDriver: true,
            tension: 100,
            friction: 8,
          }).start();
        }
      }
    };
  };

  const removeAnswer = (optionId: string, translationX: number) => {
    // Animation fade out trước khi xóa
    Animated.timing(translateXValues.current[optionId], {
      toValue: translationX > 0 ? screenWidth : -screenWidth,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setSelectedAnswers(prev => prev.filter(answer => answer.id !== optionId));
      delete translateXValues.current[optionId];
      delete panRefs.current[optionId];
    });
  };

    const panGesture = Gesture.Pan()
    .onStart(() => {
      // Có thể thêm haptic feedback hoặc visual feedback
    })
    .onUpdate((event) => {
      translateX.value = event.translationX;
    })
    .onEnd((event) => {
      if (Math.abs(event.translationX) > SWIPE_THRESHOLD) {
        // Animation xóa
        translateX.value = withTiming(
          event.translationX > 0 ? screenWidth : -screenWidth,
          { duration: 200 }
        );
        opacity.value = withTiming(0, { duration: 200 }, () => {
          runOnJS(onRemove)();
        });
      } else {
        // Trở về vị trí ban đầu
        translateX.value = withSpring(0, {
          stiffness: 150,
          damping: 15,
        });
      }
    });

  return (
    <>
      <StatusBar barStyle="light-content" />
      <AuroraBackground>
        <View
          style={[
            styles.container,
            { paddingBottom: insets.bottom, paddingTop: insets.top },
          ]}
        >
          <View style={styles.icons}>
            <ControlButtons
              onOpenSettings={() => {
                setIsSettingsModalOpen(true);
              }}
              onGoBack={onHandleGoBack}
            />
          </View>
          <View style={styles.contentContainer}>
            <CardPlayGradientBg>
              <View style={styles.cardContainer}>
                <Text style={styles.title}>
                  {state.language === 'en'
                    ? question?.question_en
                    : question?.question_vi}

                  {example && <Text style={styles.example}>{example}</Text>}
                </Text>

                {options.length > 0 && (
                  <View style={styles.optionsContainer}>
                    {options.map((item, index) => (
                      <TouchableOpacity
                        key={index}
                        onPress={() => handleSelectAnswer(item)}
                      >
                        <View style={styles.optionItem}>
                          <Text style={styles.optionItemText}>
                            {state.language === 'en'
                              ? item.text_en
                              : item.text_vi}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    ))}
                  </View>
                )}
              </View>
            </CardPlayGradientBg>
            <View style={styles.scoreContainer}>
              {selectedAnswers.length > 0
                ? selectedAnswers.map((answer, index) => (
                    <GestureDetector gesture={panGesture}
                      key={answer.id}
                    >
                      <Animated.View
                        style={[
                          styles.selectedOption,
                          {
                            transform: [
                              {
                                translateX: translateXValues.current[answer.id],
                              },
                            ],
                            zIndex: selectedAnswers.length - index,
                            bottom: index * 50,
                          },
                        ]}
                      >
                        <GradientBorderButton
                          title={answer.text_en.split('.')[0]}
                          onPress={() => {}}
                          size={60}
                          borderWidth={2}
                        />
                      </Animated.View>
                    </GestureDetector>
                  ))
                : null}
            </View>
          </View>

          <View style={styles.termsContainer}>
            <CardButton
              title={t('common.shuffle')}
              containerStyle={{ width: 'auto' }}
              onPress={onShuffleQuestion}
            />
            <CardShadowButton
              onPress={onNextQuestion}
              containerStyle={{ width: 100 }}
            >
              <ArrowRightBig width={24} height={24} />
            </CardShadowButton>
          </View>
        </View>
        <SettingsModal
          visible={isSettingsModalOpen}
          onClose={() => {
            setIsSettingsModalOpen(false);
          }}
        />
        <LanguageBS />
        <ConfirmExitModal
          visible={isConfirmExitModalOpen}
          onClose={() => {
            setIsConfirmExitModalOpen(false);
          }}
          onYes={onHandleYes}
        />
      </AuroraBackground>
    </>
  );
}

const styles = StyleSheet.create({
  blurView: {
    flex: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    fontFamily: FONTS.CRIMSON_TEXT.REGULAR,
    color: 'rgba(255, 255, 255, 0.85)',
    textAlign: 'center',
  },
  termsContainer: {
    paddingHorizontal: 24,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    textAlign: 'center',
    gap: 20,
  },
  termsLabel: {
    fontSize: 14,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: '#8E7BA1',
    lineHeight: 20,
  },
  infoContainer: {
    gap: 8,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hidden: {
    width: 'auto',
    opacity: 0,
  },
  icons: {
    width: '100%',
  },
  contentContainer: {
    paddingHorizontal: 8,
    width: '100%',
    height: 'auto',
    minHeight: 512,
  },
  cardContainer: {
    paddingVertical: 36,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    gap: 20,
    height: 'auto',
    position: 'relative',
  },
  optionsContainer: {
    justifyContent: 'center',
    alignItems: 'flex-start',
    gap: 4,
  },
  optionItem: {
    width: '100%',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  optionItemText: {
    fontSize: 20,
    fontFamily: FONTS.CRIMSON_TEXT.REGULAR,
    color: '#FFFFFFD9',
    lineHeight: 20,
  },
  example: {
    fontSize: 18,
  },
  scoreContainer: {
    position: 'absolute',
    bottom: 0,
    left: 20,
  },
  selectedOption: {
    position: 'absolute',
  },
  selectedOptionText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  dragHint: {
    color: '#fff',
    fontSize: 10,
    marginTop: 5,
    opacity: 0.8,
  },
});
