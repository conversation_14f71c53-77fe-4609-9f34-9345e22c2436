export type Question = {
  id: string; // uuid
  question_en: string;
  question_vi: string;
  topic_id?: string | null; // optional nếu có bảng topics
  created_at: string; // ISO timestamp
  example_vi?: string
  example_en?: string
  is_active?: boolean;
  question_variants?: QuestionVariant[];
}

export type QuestionVariant = {
  id: string; // uuid
  name: string;
  question_id: string; // foreign key to Question
  created_at: string; // ISO timestamp
  options?: Option[];
}

export type Option = {
  id: string; // uuid
  question_id: string; // foreign key to Question
  text_en: string;
  text_vi: string;
  is_correct: boolean;
  created_at: string; // ISO timestamp
  question_variant_id: string; // foreign key to QuestionVariant
}

export type Topic = {
  id: string; // uuid
  name_en: string;
  name_vi: string;
  created_at: string; // ISO timestamp
}

export type QuestionWithOptions = Question


export type TopicWithQuestions = Topic & {
  questions: Question[];
}

export type Database = {
  public: {
    Tables: {
      questions: {
        Row: Question;
        Insert: Question;
        Update: Partial<Question>;
      };
      options: {
        Row: Option;
        Insert: Option;
        Update: Partial<Option>;
      }
      topics: {
        Row: Topic;
        Insert: Topic;
        Update: Partial<Topic>;
      }
      question_variant: {
        Row: QuestionVariant;
        Insert: QuestionVariant;
        Update: Partial<QuestionVariant>;
      }
    }
  }
}

